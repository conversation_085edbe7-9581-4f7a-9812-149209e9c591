/**
 * @Autor: yaoshi,
 * @time: 2025-05-07,
 * @description: 顺造AI模型库，实现对多个模型的后处理
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <thread>

#include "shunzao_ai_lib.h"
#include "utils/utils.h"
#include "data_type.h"
#include "opencv2/opencv.hpp"
#include <opencv2/core.hpp>
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc.hpp"

#define EMPTY ""
// ---------------------- 配置结构 ----------------------
struct Config
{
    std::string model_file;
    std::string config_file;
    std::string input_type = "jpg";
    int model_id = 1;
    std::string images_dir;
    std::string image_path;
    std::string output_dir;
    int input_height = 700;
    int input_width = 1024;
    int log_level = 0;
};

// ---------------------- 参数解析函数 ----------------------
void parse_args(int argc, char **argv, Config &config)
{
    for (int i = 3; i < argc; ++i)
    {
        std::string arg(argv[i]);
        std::cout << arg << std::endl;
        if (arg.find("--model_file=") == 0)
            config.model_file = arg.substr(13);
        else if (arg.find("--config_file=") == 0)
            config.config_file = arg.substr(14);
        else if (arg.find("--input_type=") == 0)
            config.input_type = arg.substr(13);
        else if (arg.find("--model_id=") == 0)
        {
            std::string val = arg.substr(11);
            if (!val.empty())
                config.model_id = std::stoi(val);
            else
                std::cerr << "Warning: --model_id has empty value!" << val << std::endl;
        }
        else if (arg.find("--images_dir=") == 0)
            config.images_dir = arg.substr(13);
        else if (arg.find("--image_path=") == 0)
            config.image_path = arg.substr(13);
        else if (arg.find("--output_dir=") == 0)
            config.output_dir = arg.substr(13);
        else if (arg.find("--input_height=") == 0)
            config.input_height = std::stoi(arg.substr(15));
        else if (arg.find("--input_width=") == 0)
            config.input_width = std::stoi(arg.substr(14));
        else if (arg.find("--log_level=") == 0)
            config.log_level = std::stoi(arg.substr(12));
    }
}

#define TESTONE 0
#define TESTPARELL 0
#define SAVE_RESULTS 1
#define NUM_THREADS 2
#define USE_ORIGIN 1




int main(int argc, char **argv)
{
    printf("%s nbg input\n", argv[0]);
    if (argc < 3)
    {
        printf("Arguments count %d is incorrect!\n", argc);
        return -1;
    }
    printf("%s nbg input\n", argv[0]);
    if (argc < 3)
    {
        printf("Usage: %s <model.nbg> <input> [--model_file=...] [--config_file=...] ...\n", argv[0]);
        return -1;
    }

    Config config_;
    parse_args(argc, argv, config_);
    bool save_result = true;
    const int model_num = 1;
    const int coreid = 1;
    const int model_id_list[model_num] = {config_.model_id};
    const char *model_file_paths[model_num] = {config_.model_file.c_str()};
    const float camera_param[15] = {3.35695007e+02, 3.34332733e+02, 6.15162598e+02, 4.79834686e+02, 4.06387568e-01, 2.47576147e-01, 6.24860870e-04,
                                    -3.62452265e-04, 6.89986441e-03, 4.43165749e-01, 2.90848374e-01,
                                    2.61027496e-02, 0, 0, 0};
    struct timeval run_start{}, run_end{};
    ai_msg_t result_1;
    gettimeofday(&run_start, nullptr);
    const char *config_path = config_.config_file.c_str();
    std::string config_path_string(config_path);
    std::ifstream config_file(config_path);
    if (!config_file)
    {
        std::cout << "loading json failed " << config_path_string << std::endl;
    }
    std::stringstream buffer;
    buffer << config_file.rdbuf();
    std::string contents(buffer.str());
    // std::cout<<contents<<std::endl;

    void *p = shunzao_ai_init_from_config_interface(contents.c_str());
    // void *p = shunzao_ai_init_interface(model_file_paths, model_id_list, model_num, camera_param, 1);
    // void* p = shunzao_ai_init_from_config_interface(contents.c_str());

    gettimeofday(&run_end, nullptr);
    auto init_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                     (run_end.tv_usec - run_start.tv_usec);
    // LOG(INFO)<<"init time : "<< static_cast<double>(init_time/1000) <<" ms";
    std::cout << "init time: " << static_cast<double>(init_time) / 1000 << " ms" << std::endl;

    cv::Mat image_data_nv12;

    std::vector<cv::String> img_paths;
    cv::glob(config_.images_dir, img_paths, true);
    std::cout << "find image in dir " << config_.images_dir << std::endl;
    long total_inference_time = 0; // 总推理时间（微秒）
    int total_images = 0;
    for (int ii = 0; ii < img_paths.size(); ii++)
    {

        std::string imagepathtmp(img_paths[ii]);
        std::cout << "reading image index " << ii << "/" << img_paths.size() << ":" << imagepathtmp << std::endl;
        cv::Mat sample = cv::imread(imagepathtmp, 1); // 通过OpenCV读取图片 BGR 8位无符号整型 (CV_8U)
        if (sample.empty())
        {
            std::cout << "Failed to read image: " << imagepathtmp << std::endl;
            continue;
        }
        cv::Mat img;
        if (sample.channels() == 1)
            cv::cvtColor(sample, img, cv::COLOR_GRAY2RGB);
        else
            cv::cvtColor(sample, img, cv::COLOR_BGR2RGB);

        std::string image_path = imagepathtmp;

        std::cout << "test start..." << std::endl;

        std::vector<BBoxFlag> objects;
        gettimeofday(&run_start, nullptr);
        if (model_num == 1)
        {
            if (model_id_list[0] == 1)
            {
                objects = shunzao_ai_run_interface(p, img, coreid, model_id_list[0], &result_1, 10);
                draw_objects(img, objects, image_path);
            }
            else if (model_id_list[0] == 3)
            {
                objects = shunzao_ai_run_interface(p, img, coreid, model_id_list[0], &result_1, 10);
                draw_slam_objects(img, objects, image_path);
            }
            else
            {
                objects = shunzao_ai_run_interface(p, img, coreid, model_id_list[0], &result_1, 10);
                draw_line(img, objects, image_path);
            }
        }
        else{
            objects = shunzao_ai_run_interface(p, img, 0, 1, &result_1, 10);
            objects = shunzao_ai_run_interface(p, img, 1, 2, &result_1, 10);
        }
        gettimeofday(&run_end, nullptr);

        long inference_time_us = (run_end.tv_sec - run_start.tv_sec) * 1000000 + (run_end.tv_usec - run_start.tv_usec);
        total_inference_time += inference_time_us; // 累加到总时间
        // save_result_as_txt(img, objects, image_path);
        img.release();
        sample.release();
    }
    shunzao_ai_get_time(p);
    shunzao_ai_deinit_interface(p);
    if (img_paths.size() > 0)
    {
        double avg_inference_time_ms = static_cast<double>(total_inference_time) / img_paths.size() / 1000.0;
        std::cout << "平均推理耗时: " << avg_inference_time_ms << " ms/张" << std::endl;
        std::cout << "总处理图片数: " << img_paths.size() << std::endl;
    }
    else
    {
        std::cerr << "未处理任何图片！" << std::endl;
    }

    return 0;
}
