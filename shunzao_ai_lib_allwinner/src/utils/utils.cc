#include "utils/utils.h"
#include <opencv2/opencv.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/highgui/highgui.hpp>

BBox box_transform(BBox ori, int nn_h, int nn_w, int ori_h, int ori_w)
{

    return BBox{ori.xmin / nn_w * ori_w, ori.ymin / nn_h * ori_h,
                ori.xmax / nn_w * ori_w,
                ori.ymax / nn_h * ori_h,
                ori.label,
                ori.score};
}

BBoxXYWH xyxy2xywh(BBox &bbox)
{
    BBoxXYWH output = BBoxXYWH{bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.label, bbox.score};
    return output;
}

BBoxXYWH xyxy2xywh(BodyBox &bbox)
{
    BBoxXYWH output = BBoxXYWH{bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.label, bbox.score};
    return output;
}

std::vector<BBoxXYWH> xyxy2xywh(std::vector<BBox> &bboxes)
{
    std::vector<BBoxXYWH> output;
    for (int i = 0; i < int(bboxes.size()); ++i)
    {
        BBox bbox = bboxes[i];
        // output.push_back(BBoxXYWH{ bbox.xmin, bbox.ymin, bbox.xmax - bbox.xmin, bbox.ymax - bbox.ymin, bbox.score });
        output.push_back(xyxy2xywh(bbox));
    }
    return output;
}

cv::Point2f rotate_point(cv::Point2f pt, float angle_rad)
{
    float sn = sin(angle_rad);
    float cs = cos(angle_rad);
    return cv::Point2f(pt.x * cs - pt.y * sn, pt.x * sn + pt.y * cs);
}

cv::Point2f get_3rd_point(cv::Point2f a, cv::Point2f b)
{
    cv::Point2f direction = a - b;
    cv::Point2f third_pt = b + cv::Point2f(-direction.y, direction.x);
    return third_pt;
}

cv::Mat get_affine_transform(CenterScale cs, float rot, Size output_size, cv::Point2f shift = cv::Point2f(0., 0.))
{
    cv::Point2f center = cs.center;
    cv::Point2f scale_tmp = cv::Point2f(cs.scale.x * 200., cs.scale.y * 200.);
    float src_w = scale_tmp.x;
    float dst_w = float(output_size.w);
    float dst_h = float(output_size.h);
    float rot_rad = PI * rot / 180;
    cv::Point2f src_dir = rotate_point(cv::Point2f(0., src_w * -0.5), rot_rad);
    cv::Point2f dst_dir = cv::Point2f(0., dst_w * -0.5);
    cv::Point2f src[3];
    src[0] = cv::Point2f(center.x + scale_tmp.x * shift.x, center.y + scale_tmp.y * shift.y);
    src[1] = cv::Point2f(center.x + src_dir.x + scale_tmp.x * shift.x, center.y + src_dir.y + scale_tmp.y * shift.y);
    src[2] = get_3rd_point(src[0], src[1]);
    cv::Point2f dst[3];
    dst[0] = cv::Point2f(dst_w * 0.5, dst_h * 0.5);
    dst[1] = cv::Point2f(dst_w * 0.5 + dst_dir.x, dst_h * 0.5 + dst_dir.y);
    dst[2] = get_3rd_point(dst[0], dst[1]);
    cv::Mat trans = cv::getAffineTransform(src, dst);
    return trans;
}

std::vector<CropImage> crop_image_by_bbox(cv::Mat image, std::vector<BBoxXYWH> &bboxes, Size input_size, float scale_rate)
{
    std::vector<CropImage> r_image;
    for (int i = 0; i < int(bboxes.size()); ++i)
    {
        BBoxXYWH bbox = bboxes[i];
        CenterScale cs = get_box_cs(bbox, input_size, scale_rate);
        cv::Mat trans = get_affine_transform(cs, 0, input_size);
        cv::Mat img;
        cv::warpAffine(image, img, trans, cv::Size(int(input_size.w), int(input_size.h)), cv::INTER_LINEAR);
        r_image.push_back(CropImage{img, cs});
    }
    return r_image;
}

cv::Point2f transform_preds(cv::Point2f coords, CenterScale cs, Size output_size)
{
    cv::Point2f center = cs.center;
    cv::Point2f scale = cs.scale;
    cv::Point2f result;
    scale = scale * 200.;
    float scale_x = scale.x / (output_size.w - 1.0);
    float scale_y = scale.y / (output_size.h - 1.0);
    result.x = coords.x * scale_x + center.x - scale.x * 0.5;
    result.y = coords.y * scale_y + center.y - scale.y * 0.5;
    return result;
}

// HKP pixcel_camera_transfer(HKP sk, std::array<float, 2> focal_cam1, std::array<float, 2> princpt_cam1,
//     std::array<float, 2> focal_cam2, std::array<float, 2> princpt_cam2) {
//     HKP now_kp;
//     for (int i = 0; i < sk.size(); i++) {
//         KeyPoint new_kp;
//         float u = (focal_cam2[0] / focal_cam1[0]) * (sk[i].point.x - princpt_cam1[0]) + princpt_cam2[0];
//         float v = (focal_cam2[1] / focal_cam1[1]) * (sk[i].point.y - princpt_cam1[1]) + princpt_cam2[1];
//         new_kp.point.x = u;
//         new_kp.point.y = v;
//         new_kp.score = sk[i].score;
//         now_kp[i] = new_kp;
//     }
//     return now_kp;
// }

// void pixel2cam(HKP3D& pixel_coord, std::array<float, 2> f, std::array<float, 2> c) {
//     for (int i = 0; i < pixel_coord.size(); i++) {
//         pixel_coord[i].point.x = (pixel_coord[i].point.x - c[0]) / f[0] * pixel_coord[i].point.z;
//         pixel_coord[i].point.y = (pixel_coord[i].point.y - c[1]) / f[1] * pixel_coord[i].point.z;
//     }
// }

void draw_box(cv::Mat image, BBox bbox, cv::Scalar color)
{
    cv::rectangle(image, cv::Rect(cv::Point((int)bbox.xmin, (int)bbox.ymin), cv::Point((int)bbox.xmax, (int)bbox.ymax)), color, 2);
}

float get_iou(BBox b1, BBox b2)
{
    float w = std::min(b1.xmax, b2.xmax) - std::max(b1.xmin, b2.xmin);
    w = (w < 0 ? 0 : w);
    float h = std::min(b1.ymax, b2.ymax) - std::max(b1.ymin, b2.ymin);
    h = (h < 0 ? 0 : h);

    float iou = w * h / ((b1.xmax - b1.xmax) * (b1.ymax - b1.ymin) + (b2.xmax - b2.xmax) * (b2.ymax - b2.ymin) - w * h);

    return iou;
}

CenterScale get_box_cs(BBoxXYWH box, Size input_size, float scale_rate)
{
    float x = box.xmin;
    float y = box.ymin;
    float w = box.w;
    float h = box.h;
    float aspect_ratio = float(input_size.w) / float(input_size.h);
    cv::Point2f center = cv::Point2f(x + w * 0.5, y + h * 0.5);
    if (w > aspect_ratio * h)
    {
        h = w * 1.0 / aspect_ratio;
    }
    else if (w < aspect_ratio * h)
    {
        w = h * aspect_ratio;
    }
    cv::Point2f scale = cv::Point2f(w * 1.0 / 200.0 * scale_rate, h * 1.0 / 200.0 * scale_rate);
    return CenterScale{center, scale};
}

float CalculateIOU(const BBox &a, const BBox &b)
{
    // 计算交集区域
    float inter_x1 = std::max(a.xmin, b.xmin);
    float inter_y1 = std::max(a.ymin, b.ymin);
    float inter_x2 = std::min(a.xmax, b.xmax);
    float inter_y2 = std::min(a.ymax, b.ymax);

    // 计算交集面积
    float inter_w = std::max(0.0f, inter_x2 - inter_x1);
    float inter_h = std::max(0.0f, inter_y2 - inter_y1);
    float inter_area = inter_w * inter_h;

    // 计算并集面积
    float area_a = (a.xmax - a.xmin) * (a.ymax - a.ymin);
    float area_b = (b.xmax - b.xmin) * (b.ymax - b.ymin);
    float union_area = area_a + area_b - inter_area;

    // 避免除零错误
    if (union_area <= 0)
        return 0.0f;

    return inter_area / union_area;
}

int NMS(std::vector<BBox> &detectRects, float nms_thresh)
{
    // 1. 按类别分组
    std::unordered_map<int, std::vector<BBox>> classRects;
    for (auto &rect : detectRects)
    {
        classRects[rect.label].push_back(rect);
    }

    detectRects.clear(); // 清空原始结果

    // 2. 对每个类别单独进行NMS
    for (auto &[classId, rects] : classRects)
    {
        // 按置信度降序排序
        std::sort(rects.begin(), rects.end(),
                  [](const BBox &a, const BBox &b)
                  {
                      return a.score > b.score;
                  });

        // 3. 执行NMS抑制
        for (size_t i = 0; i < rects.size(); ++i)
        {
            if (rects[i].score < 0)
                continue; // 已被抑制

            detectRects.push_back(rects[i]); // 保留当前框

            for (size_t j = i + 1; j < rects.size(); ++j)
            {
                if (rects[j].score < 0)
                    continue; // 跳过已抑制框

                // 计算IOU（确保实现正确）
                float iou = CalculateIOU(rects[i], rects[j]);

                // 抑制重叠框
                if (iou > nms_thresh)
                {
                    rects[j].score = -1; // 标记为抑制
                }
            }
        }
    }

    return detectRects.size(); // 返回保留的检测框数量
}

float sigmoid(float x)
{
    // return 1 / (1 + fast_exp(-x));
    return 1 / (1 + expf(-x));
}

int GenerateMeshgrid(int headNum, int mapSize[1][2], std::vector<float> &meshgrid)
{
    int ret = 0;
    if (headNum == 0)
    {
        printf("=== yolov8 Meshgrid  Generate failed! \n");
    }

    for (int index = 0; index < headNum; index++)
    {
        for (int i = 0; i < mapSize[index][0]; i++)
        {
            for (int j = 0; j < mapSize[index][1]; j++)
            {
                meshgrid.push_back(float(j + 0.5));
                meshgrid.push_back(float(i + 0.5));
            }
        }
    }

    return ret;
}

void draw_objects(cv::Mat &image_data, const std::vector<BBoxFlag> &objects, std::string image_path)
{

    static const char *class_names[] = {"Trash can", "Cleaning cloth", "Rug", "Shoes", "Wire", "Sliding rail", "Wheels",
                                        "seat_base", "scales",
                                        "sandbasin", "bei_bowl", "wan_bowl", "big_shack", "sml_shack",
                                        "shit"};

    std::string result_path = image_path;
    size_t pos = result_path.find("input_data");
    if (pos != std::string::npos)
    {
        result_path.replace(pos, std::string("input_data").length(), "output");
    }
    std::cout << "pic path: " << result_path << std::endl;

    cv::Mat image;

    cv::cvtColor(image_data, image, cv::COLOR_RGB2BGR); // 转为BGR格式保存

    for (size_t i = 0; i < objects.size(); i++)
    {
        const BBox &obj = objects[i].box;
        cv::rectangle(image, cv::Point(obj.xmin, obj.ymin), cv::Point(obj.xmax, obj.ymax), cv::Scalar(255, 0, 0), 2);

        char text[256];
        sprintf(text, "%s %.1f%%", class_names[obj.label], obj.score * 100);

        int baseLine = 0;
        cv::Size label_size = cv::getTextSize(text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);

        int x = obj.xmin;
        int y = obj.ymin - label_size.height - baseLine;
        if (y < 0)
            y = 0;
        if (x + label_size.width > image.cols)
            x = image.cols - label_size.width;

        cv::rectangle(image, cv::Rect(cv::Point(x, y), cv::Size(label_size.width, label_size.height + baseLine)),
                      cv::Scalar(255, 255, 255), -1);

        cv::putText(image, text, cv::Point(x, y + label_size.height), cv::FONT_HERSHEY_SIMPLEX, 0.5,
                    cv::Scalar(0, 0, 0));

        char coord_text[100];
        cv::putText(image, coord_text, cv::Point(obj.xmax - 70, obj.ymax - 2),
                    cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 255, 0), 1);
    }

    cv::imwrite(result_path, image);
}

void draw_slam_objects(cv::Mat &image_data, const std::vector<BBoxFlag> &objects, std::string image_path)
{
    // SLAM检测的6个类别名称
    static const char *slam_class_names[] = {
        "bed_grounded",      // 0
        "bed_highleg",       // 1
        "sofa_grounded",     // 2
        "sofa_highleg",      // 3
        "door",              // 4
        "dining_table_set"   // 5
    };

    // 为不同类别定义不同的颜色 (BGR格式)
    static const cv::Scalar slam_colors[] = {
        cv::Scalar(255, 0, 0),    // 蓝色 - bed_grounded
        cv::Scalar(0, 255, 0),    // 绿色 - bed_highleg
        cv::Scalar(0, 0, 255),    // 红色 - sofa_grounded
        cv::Scalar(255, 255, 0),  // 青色 - sofa_highleg
        cv::Scalar(255, 0, 255),  // 紫色 - door
        cv::Scalar(0, 255, 255)   // 黄色 - dining_table_set
    };

    std::string result_path = image_path;
    size_t pos = result_path.find("input_data");
    if (pos != std::string::npos)
    {
        result_path.replace(pos, std::string("input_data").length(), "output");
    }
    std::cout << "[SLAM] Saving result to: " << result_path << std::endl;

    cv::Mat image;
    cv::cvtColor(image_data, image, cv::COLOR_RGB2BGR); // 转为BGR格式保存

    for (size_t i = 0; i < objects.size(); i++)
    {
        const BBox &obj = objects[i].box;

        // 确保类别索引在有效范围内
        int class_id = obj.label;
        if (class_id < 0 || class_id >= 6) {
            std::cout << "[SLAM] Warning: Invalid class_id " << class_id << ", skipping..." << std::endl;
            continue;
        }

        // 使用对应类别的颜色绘制边界框
        cv::Scalar color = slam_colors[class_id];
        cv::rectangle(image, cv::Point(obj.xmin, obj.ymin), cv::Point(obj.xmax, obj.ymax), color, 2);

        // // 绘制中心点（除了door类别）
        // if (class_id != 4) { // door类别不绘制中心点
        //     int cx = (obj.xmin + obj.xmax) / 2;
        //     int cy = (obj.ymin + obj.ymax) / 2;
        //     cv::circle(image, cv::Point(cx, cy), 3, cv::Scalar(0, 0, 255), -1); // 红色填充圆
        // }

        // 绘制置信度分数
        char confidence_text[50];
        sprintf(confidence_text, "%.2f", obj.score);

        cv::Size text_size = cv::getTextSize(confidence_text, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, nullptr);

        // 绘制置信度背景
        cv::rectangle(image,
                     cv::Point(obj.xmin, obj.ymin - text_size.height - 5),
                     cv::Point(obj.xmin + text_size.width, obj.ymin),
                     color, -1);

        // 绘制置信度文本
        cv::putText(image, confidence_text,
                   cv::Point(obj.xmin, obj.ymin - 5),
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0), 1);
    }

    // 绘制图例
    draw_slam_legend(image, slam_class_names, slam_colors);

    cv::imwrite(result_path, image);
    std::cout << "[SLAM] Detection result saved with " << objects.size() << " objects" << std::endl;
}

void draw_line(cv::Mat &image_data, const std::vector<BBoxFlag> &objects, std::string image_path)
{
    std::string result_path = image_path;
    size_t pos = result_path.find("input_data");
    if (pos != std::string::npos)
    {
        result_path.replace(pos, std::string("input_data").length(), "output");
    }

    cv::Mat image;
    cv::cvtColor(image_data, image, cv::COLOR_RGB2BGR); // 转为BGR格式保存

    for (size_t i = 0; i < objects.size(); i++)
    {
        const BBox &obj = objects[i].box;
        // std::cout<<"obj.xmin:"<<obj.xmin<<", obj.ymin:"<<obj.ymin<<std::endl;
        cv::circle(image, cv::Point(obj.xmin, obj.ymin), 3, cv::Scalar(255, 0, 0), -1);
    }
    cv::imwrite(result_path, image);
}

void save_result_as_txt(cv::Mat &image_data, const std::vector<BBoxFlag> &objects, std::string image_path)
{

    static const char *class_names[] = {"Trash can", "Cleaning cloth", "Rug", "Shoes", "Wire", "Sliding rail", "Wheels",
                                        "seat_base", "scales",
                                        "sandbasin", "bei_bowl", "wan_bowl", "big_shack", "sml_shack",
                                        "shit"};
    std::string result_path = image_path;
    size_t pos = result_path.find("input_data");
    if (pos != std::string::npos)
    {
        result_path.replace(pos, std::string("input_data").length(), "result");
    }
    std::cout << "Result path: " << result_path << std::endl;
    std::string txt_path = result_path;
    size_t last_dot = txt_path.find_last_of(".");
    if (last_dot != std::string::npos)
    {
        txt_path = txt_path.substr(0, last_dot);
    }
    txt_path += ".txt";
    std::ofstream out_file(txt_path);
    if (!out_file.is_open())
    {
        std::cerr << "Error: Cannot open output file: " << txt_path << std::endl;
        return;
    }
    for (size_t i = 0; i < objects.size(); i++)
    {
        const BBox &obj = objects[i].box;

        // 在文本文件中写入结果: 类别 xmin ymin xmax ymax
        out_file << obj.label << " "
                 << static_cast<int>(obj.xmin) << " "
                 << static_cast<int>(obj.ymin) << " "
                 << static_cast<int>(obj.xmax) << " "
                 << static_cast<int>(obj.ymax) << " "
                 << obj.score << std::endl;
    }
    out_file.close();
}

void draw_slam_legend(cv::Mat &image, const char* slam_class_names[], const cv::Scalar slam_colors[])
{
    // 图例配置
    const int LEGEND_RECT_WIDTH = 15;
    const int LEGEND_RECT_X_OFFSET = 10;
    const int LEGEND_X_STEP = 150;
    const int LEGEND_TEXT_X_OFFSET = 20;
    const int LEGEND_TEXT_Y_OFFSET = 5;
    const int LEGEND_Y_OFFSET_2 = image.rows - 90;  // bed类别
    const int LEGEND_Y_OFFSET_3 = image.rows - 60;  // sofa类别
    const int LEGEND_Y_OFFSET_4 = image.rows - 30;  // door和dining_table类别
    const cv::Scalar TEXT_COLOR = cv::Scalar(255, 255, 255); // 白色文本
    const float FONT_SIZE = 0.4;
    const int FONT_THICKNESS = 1;

    // 绘制bed类别图例
    for (int i = 0; i < 2; i++) {
        int x = LEGEND_RECT_X_OFFSET + i * LEGEND_X_STEP;
        int y = LEGEND_Y_OFFSET_2;

        // 绘制颜色矩形
        cv::rectangle(image, cv::Point(x, y - 10), cv::Point(x + LEGEND_RECT_WIDTH, y + 5), slam_colors[i], -1);

        // 绘制文本
        cv::putText(image, slam_class_names[i], cv::Point(x + LEGEND_TEXT_X_OFFSET, y + LEGEND_TEXT_Y_OFFSET),
                   cv::FONT_HERSHEY_SIMPLEX, FONT_SIZE, TEXT_COLOR, FONT_THICKNESS);
    }

    // 绘制sofa类别图例
    for (int i = 0; i < 2; i++) {
        int x = LEGEND_RECT_X_OFFSET + i * LEGEND_X_STEP;
        int y = LEGEND_Y_OFFSET_3;

        // 绘制颜色矩形
        cv::rectangle(image, cv::Point(x, y - 10), cv::Point(x + LEGEND_RECT_WIDTH, y + 5), slam_colors[i + 2], -1);

        // 绘制文本
        cv::putText(image, slam_class_names[i + 2], cv::Point(x + LEGEND_TEXT_X_OFFSET, y + LEGEND_TEXT_Y_OFFSET),
                   cv::FONT_HERSHEY_SIMPLEX, FONT_SIZE, TEXT_COLOR, FONT_THICKNESS);
    }

    // 绘制door和dining_table类别图例
    for (int i = 0; i < 2; i++) {
        int x = LEGEND_RECT_X_OFFSET + i * LEGEND_X_STEP;
        int y = LEGEND_Y_OFFSET_4;

        // 绘制颜色矩形
        cv::rectangle(image, cv::Point(x, y - 10), cv::Point(x + LEGEND_RECT_WIDTH, y + 5), slam_colors[i + 4], -1);

        // 绘制文本
        cv::putText(image, slam_class_names[i + 4], cv::Point(x + LEGEND_TEXT_X_OFFSET, y + LEGEND_TEXT_Y_OFFSET),
                   cv::FONT_HERSHEY_SIMPLEX, FONT_SIZE, TEXT_COLOR, FONT_THICKNESS);
    }
}

