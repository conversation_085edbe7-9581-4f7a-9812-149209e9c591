#include "npulib.h"
#include <iostream>
#include <cstdlib>  // for free
#include "base_model.h"
#include <opencv2/opencv.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/highgui/highgui.hpp>

NetworkBase::NetworkBase() {
    // 可选：初始化成员变量
    // init(model_file_path, network_id, priority);
}

NetworkBase::~NetworkBase() {
    // for (int i = 0; i < output_cnt_num; i++) {
    //     delete[] output_data[i];
    //     output_data[i] = nullptr;
    // }
    // std::cout<<"NetworkBase::~NetworkBase"<<std::endl;
    release();  // 析构时自动释放资源
}

bool NetworkBase::init(const char* model_file_path, unsigned int network_id, unsigned char priority) {
    
    // nn_in_height_ ;

    std::cout<<"network_create from :"<<model_file_path<<std::endl;
    if (network_create((char*)model_file_path, network_id) != 0) {
        std::cerr << "[NetworkBase] Failed to create network." << std::endl;
        return false;
    }

    // if (set_priority(priority) != 0) {
    //     std::cerr << "[NetworkBase] Failed to set priority." << std::endl;
    //     return false;
    // }

    // m_mem_pool_size = get_memory_pool_size();
    // if (m_mem_pool_size == 0) {
    //     std::cerr << "[NetworkBase] Invalid memory pool size." << std::endl;
    //     return false;
    // }

    // m_mem_pool_buffer = create_memory_pool_buffer(m_mem_pool_size);
    // if (!m_mem_pool_buffer || set_memory_pool_buffer(m_mem_pool_buffer) != 0) {
    //     std::cerr << "[NetworkBase] Failed to set memory pool buffer." << std::endl;
    //     return false;
    // }

    // if (network_prepare() != 0 || network_input_output_set() != 0) {
    //     std::cerr << "[NetworkBase] Failed to prepare network." << std::endl;
    //     return false;
    // }
    if (network_prepare() != 0) {
        std::cerr << "[NetworkBase] Failed to prepare network." << std::endl;
        return false;
    }
    std::cout << "[NetworkBase] Network initialized successfully." << std::endl;
    return true;
}

int NetworkBase::load_input_set_output(void *file_data, unsigned int file_size) 
{
    // std::cout<<"network_input_output_set start..."<<std::endl;
    int ret = 0;
    if (network_load_input_buffer(file_data, file_size) != 0) {
        std::cerr << "[GroundDet] Failed to load input buffer." << std::endl;
        return false;
    }
    if (file_data != nullptr) {
        free(file_data);
        file_data = nullptr;
    }

    if (get_output_cnt() != 0){
        output_cnt_num = get_output_cnt();
        // std::cout<<"get_output_cnt num: "<<output_cnt_num<<std::endl;
    }

    output_data = new float*[output_cnt_num];
    for (int i = 0; i < output_cnt_num; i++){
        output_data[i] = new float[m_output_data_len[i]];
        // std::cout<<"output_data["<<i<<"] len: "<<m_output_data_len[i]<<std::endl;
    }

    ret = this->network_input_output_set();
    if (ret != 0) {
        printf("set network input/output failed.\n");
        ret = -1;
        return ret;
    }
    // std::cout<<"network_input_output_set done"<<std::endl;
    return ret;

}

void get_input_data(cv::Mat& img, unsigned char* input_data, int letterbox_rows, int letterbox_cols,
    const float* mean, const float* scale)
{
    cv::Mat img_input;

    /* letterbox process to support different letterbox size */
    float scale_letterbox;
    if ((letterbox_rows * 1.0 / img.rows) < (letterbox_cols * 1.0 / img.cols))
    {
        scale_letterbox = letterbox_rows * 1.0 / img.rows;
    }
    else
    {
        scale_letterbox = letterbox_cols * 1.0 / img.cols;
    }
    int resize_cols = int(scale_letterbox * img.cols);
    int resize_rows = int(scale_letterbox * img.rows);

    cv::resize(img, img_input, cv::Size(resize_cols, resize_rows));
    std::cout<<"cv::resize img OK ..."<<std::endl;
    // img.convertTo(img, CV_32FC3);
    // Generate a gray image for letterbox using opencv
    cv::Mat img_new(letterbox_cols, letterbox_rows, CV_8UC3, cv::Scalar(0, 0, 0));
    // cv::Mat img_new(letterbox_cols, letterbox_rows, CV_32FC3, cv::Scalar(0.5 / scale[0] + mean[0], 0.5 / scale[1] + mean[1], 0.5 / scale[2] + mean[2]));
    int top = (letterbox_rows - resize_rows) / 2;
    int bot = (letterbox_rows - resize_rows + 1) / 2;
    int left = (letterbox_cols - resize_cols) / 2;
    int right = (letterbox_cols - resize_cols + 1) / 2;
    // Letterbox filling
    cv::copyMakeBorder(img_input, img_new, top, bot, left, right, cv::BORDER_CONSTANT, cv::Scalar(114, 114, 114));
    // img_new.convertTo(img_new, CV_32FC3);
    // float* img_data = (float*)img_new.data;
    uint8_t* img_data = (uint8_t*)img_new.data;

    // memcpy(input_data, img_data, 640*640*3);

    /* nhwc to nchw */
    for (int h = 0; h < letterbox_rows; h++)
    {
        for (int w = 0; w < letterbox_cols; w++)
        {
            for (int c = 0; c < 3; c++)
            {
                int in_index = h * letterbox_cols * 3 + w * 3 + c;
                int out_index = c * letterbox_rows * letterbox_cols + h * letterbox_cols + w;

                // input dequant
                input_data[out_index] = (unsigned char)(img_data[in_index]);	//uint8
                // input_data[out_index] = (int8_t)(img_data[in_index] - 128);	//pcq int8
                // input_data[out_index] = img_data[in_index] / 255.0f; // 归一化为 float32
                // input_data[out_index] = (img_data[in_index] - mean[c]) * scale[c];

                // std::cout << "input_data[" << out_index << "]: " << (int)input_data[out_index] << std::endl;
            }
        }
    }
}

uint8_t * NetworkBase::preprocess(cv::Mat& img, int coreid, unsigned int *file_size)
{
    std::cout<<"img preprocess.cpp run..."<<std::endl;

	// set default  size
	int img_size = nn_in_height_ * nn_in_width_ * nn_in_channel_;
    std::cout<<"img_size:"<<img_size<<std::endl;
    std::cout<<"nn_in_height_:"<<nn_in_height_<<std::endl;
    std::cout<<"nn_in_width_:"<<nn_in_width_<<std::endl;
    std::cout<<"nn_in_channel_:"<<nn_in_channel_<<std::endl;

	*file_size = img_size * sizeof(uint8_t);

	uint8_t *tensorData = NULL;
	tensorData = (uint8_t *)malloc(1 * img_size * sizeof(uint8_t));

    // std::string img_path = image_data->path;
    // std::cout<<"img_path:"<<img_path<<std::endl;
    std::cout << "mean[0]:"<<mean[0]<<std::endl;
    std::cout << "mean[1]:"<<mean[1]<<std::endl;
    std::cout << "mean[2]:"<<mean[2]<<std::endl;
    std::cout << "scale[0]:"<<scale[0]<<std::endl;
    std::cout << "scale[1]:"<<scale[1]<<std::endl;
    std::cout << "scale[2]:"<<scale[2]<<std::endl;
    get_input_data(img, tensorData, nn_in_height_, nn_in_width_, mean, scale);
    std::cout<<"get_input_data OK ..."<<std::endl;
    return tensorData;
}

bool NetworkBase::run_once() {
    std::cout<<"network_run..."<<std::endl;
    if (network_run() != 0) {
        std::cerr << "[NetworkBase] Failed to run network." << std::endl;
        return false;
    }
    return true;
}

void NetworkBase::delete_output_data(float** output_data){
    if (output_data != nullptr) {
        for (int i = 0; i < output_cnt_num; ++i) {
            if (output_data[i] != nullptr) {
                free(output_data[i]);  //释放每个 float*
                output_data[i] = nullptr;
            }
        }
        free(output_data);  // 释放 float* 指针数组
        output_data = nullptr;
    }
}

float** NetworkBase::get_output_data(float **output_data) {
    return get_output(output_data);  // 默认 SAVE_NONE
}

void NetworkBase::release() {
    // std::cout<<"network_release..."<<std::endl;
    network_finish();
    network_destroy();
    // if (m_mem_pool_buffer) {
    //     free(m_mem_pool_buffer);  // 假设 SDK 内部用 malloc 分配的
    //     m_mem_pool_buffer = nullptr;
    // }
}
