#include "slam_det.h"
#include "utils/utils.h"
#include "shunzao_ai_task.h"
#include "shunzao_ai_lib.h"
#include "yolov5.h"
#include "yolov8.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
extern int PRINT_LOG;

SlamDet::SlamDet(const char* model_path){
    if (!setup(model_path)) {
        std::cerr << "[SlamDet] Constructor failed to setup with model: " << model_path << std::endl;
    }
    res_addr_ = (char*)malloc(max_data_size_);
}

SlamDet::~SlamDet(){
    if(res_addr_){
        delete res_addr_;
        res_addr_ = nullptr;
    }
}

bool SlamDet::setup(const std::string& model_path) {
    // 使用 network_id = 0, 默认优先级 = 128
    nn_in_width_ = input_h;
    nn_in_height_ = input_w;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}

int SlamDet::loadconfig(std::string config_string){
    rapidjson::Document document;
    document.Parse(config_string.data());

    if (document.HasParseError()) {
        std::cout<<"[SlamDet] parsing config file error"<<std::endl;
        return -1;
    }
    
	if(document.HasMember("config")){
        rapidjson::Value& model_value = document["config"];
        if(model_value.HasMember("score_threshold")){
            score_threshold_ = model_value["score_threshold"].GetFloat();
        }
        if(model_value.HasMember("nms_threshold")){
            nms_threshold_ = model_value["nms_threshold"].GetFloat();
        }
		if(model_value.HasMember("mean_score")){
			mean_score_.clear();
            auto mean_score_array = model_value["mean_score"].GetArray();
			for (size_t i = 0; i < mean_score_array.Size(); i++)
			{
				mean_score_.push_back(mean_score_array[i].GetFloat());
			}
		}
        if(model_value.HasMember("heads_list")){
            heads_list.clear();
            auto heads_array = model_value["heads_list"].GetArray();
            for (size_t i = 0; i < heads_array.Size(); i++)
            {
                heads_list.push_back(heads_array[i].GetInt());
            }
        }
        if(model_value.HasMember("class_num")){
            class_num = model_value["class_num"].GetInt();
        }
    }
    
    std::cout << "[SlamDet] Config loaded - score_threshold: " << score_threshold_ 
              << ", nms_threshold: " << nms_threshold_ 
              << ", class_num: " << class_num << std::endl;
    
    return 0;
}

#include <chrono>

bool SlamDet::run_inference_with_time(cv::Mat& img, InputParam* inp) {
    // 记录总开始时间
    auto total_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "[SlamDet] Running inference..." << std::endl;
    int ret = 0;

    // 1. 预处理阶段计时
    std::cout << "[SlamDet] Preprocess start" << std::endl;
    auto preprocess_start = std::chrono::high_resolution_clock::now();
    file_data = this->preprocess(img, inp->coreid, &file_size);
    auto preprocess_end = std::chrono::high_resolution_clock::now();
    auto preprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(
        preprocess_end - preprocess_start);
    std::cout << "[SlamDet] Preprocess: " << preprocess_time.count() /1000.0 << " ms" << std::endl;

    // 2. 推理阶段计时
    std::cout << "[SlamDet] Inference start" << std::endl;
    auto inference_start = std::chrono::high_resolution_clock::now();
    ret = load_input_set_output(file_data, file_size);
    if (!run_once()) {
        std::cerr << "[SlamDet] Network run failed." << std::endl;
        return false;
    }
    get_output_data(output_data);
    auto inference_end = std::chrono::high_resolution_clock::now();
    auto inference_time = std::chrono::duration_cast<std::chrono::microseconds>(
        inference_end - inference_start);
    std::cout << "[SlamDet] Inference: " << inference_time.count() /1000.0 << " ms" << std::endl;

    if (!output_data) {
        std::cerr << "[SlamDet] Failed to get output." << std::endl;
        return false;
    }

    // 3. 后处理阶段计时
    std::cout << "[SlamDet] Postprocess start" << std::endl;
    auto postprocess_start = std::chrono::high_resolution_clock::now();
    if (this->postprocess(img, output_data) !=0){
        std::cerr << "[SlamDet] postprocess failed." << std::endl;
        return -1;
    }
    auto postprocess_end = std::chrono::high_resolution_clock::now();
    auto postprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(
        postprocess_end - postprocess_start);
    std::cout << "[SlamDet] Postprocess: " << postprocess_time.count() /1000.0 << " ms" << std::endl;

    // 总时间
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
        total_end - total_start);
    std::cout << "[SlamDet] Total: " << total_time.count() /1000.0 << " ms" << std::endl;

    return true;
}

bool SlamDet::run_inference(cv::Mat& img, InputParam* inp) {

    std::cout << "[SlamDet] Running inference..." << std::endl;
    int ret = 0;

    file_data = this->preprocess(img, inp->coreid, &file_size);

    ret = load_input_set_output(file_data, file_size);

    if (!run_once()) {
        std::cerr << "[SlamDet] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵
    if (!output_data) {
        std::cerr << "[SlamDet] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(img, output_data) !=0){
        std::cerr << "[SlamDet] postprocess failed." << std::endl;
        return -1;
    }

    return true;
}

bool SlamDet::run_inference(cv::Mat& img, InputParam* inp, shunzaoAiTask* outer) {

    std::cout << "[SlamDet] Running inference..." << std::endl;
    int ret = 0;
    struct  timeval run_start{},run_end{};
    gettimeofday(&run_start,nullptr);
    file_data = this->preprocess(img, inp->coreid, &file_size);
    ret = load_input_set_output(file_data, file_size);
    gettimeofday(&run_end,nullptr);
    long long preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
    (run_end.tv_usec - run_start.tv_usec);
    outer->setPreprocessTime(preprocess_time);

    gettimeofday(&run_start,nullptr);
    if (!run_once()) {
        std::cerr << "[SlamDet] Network run failed." << std::endl;
        return false;
    }
    get_output_data(output_data);  // 获取网络最终的输出矩阵
    gettimeofday(&run_end,nullptr);
    preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 + (run_end.tv_usec - run_start.tv_usec);
    outer->setInferenceTime(preprocess_time);

    if (!output_data) {
        std::cerr << "[SlamDet] Failed to get output." << std::endl;
        return false;
    }
    gettimeofday(&run_start,nullptr);
    if (this->postprocess(img, output_data) !=0){
        std::cerr << "[SlamDet] postprocess failed." << std::endl;
        return -1;
    }
    gettimeofday(&run_end,nullptr);
    preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 + (run_end.tv_usec - run_start.tv_usec);
    outer->setPostprocessTime(preprocess_time);

    return true;
}

std::vector<BBoxFlag> SlamDet::get_prediction(int* size) {
    std::cout<< "[SlamDet] get_prediction... "<<std::endl;

	std::vector<BBoxFlag> tmp;
    det_boxes_flag_results.clear();

	for (int i = 0; i < results_.size(); i++) {
        float flag = 1.0; // SLAM检测默认flag为1
        det_boxes_flag_results.push_back(BBoxFlag{results_[i], flag});
	}

    return det_boxes_flag_results;
}

int SlamDet::postprocess(cv::Mat& img, float** output_tensor){
    std::cout<<"[SlamDet] postprocess..."<<std::endl;
    std::cout << "[SlamDet] heads_list.size():" << heads_list.size() << std::endl;
    std::cout << "[SlamDet] Original image size: " << img.cols << "x" << img.rows << std::endl;
    std::cout << "[SlamDet] Network input size: " << input_w << "x" << input_h << std::endl;

    std::vector<BBox> det_boxes;
    results_.clear(); // 清空results_

    // SLAM检测只有一个头，三个尺度，检测6个类别
    // output_tensor包含3个尺度的输出：[0]=80x80, [1]=40x40, [2]=20x20
    float** output_head = new float*[3];    // 分配新的指针数组，用于存储3个尺度tensor的指针

    // 将三个尺度的输出传递给yolov8后处理
    for (int j = 0; j < 3; ++j) {
        output_head[j] = output_tensor[j];
        std::cout << "[SlamDet] output_head[" << j << "]: " << output_head[j] << std::endl;
    }

    // 使用yolov8后处理，处理6个类别
    det_boxes = yolov8_post_process(output_head, class_num, input_w, input_h, score_threshold_);
    std::cout << "[SlamDet] yolov8_post_process returned " << det_boxes.size() << " boxes" << std::endl;

    // 将检测结果添加到总结果中，并进行坐标还原
    for(auto& box : det_boxes) {
        // 坐标还原：从网络输入尺寸(640x640)还原到原图尺寸
        BBox transformed_box = box_transform(box, input_h, input_w, img.rows, img.cols);

        // 检查还原后的坐标是否合理
        if (transformed_box.xmin >= 0 && transformed_box.ymin >= 0 &&
            transformed_box.xmax <= img.cols && transformed_box.ymax <= img.rows &&
            transformed_box.xmax > transformed_box.xmin && transformed_box.ymax > transformed_box.ymin &&
            transformed_box.score > 0.01 && transformed_box.score <= 1.0 &&
            transformed_box.label >= 0 && transformed_box.label < class_num) {
            results_.push_back(transformed_box);
        } else {
            std::cout << "[SlamDet] Filtered invalid transformed box: ("
                      << transformed_box.xmin << "," << transformed_box.ymin
                      << "," << transformed_box.xmax << "," << transformed_box.ymax
                      << ") score:" << transformed_box.score
                      << " label:" << transformed_box.label << std::endl;
        }
    }

    std::cout << "[SlamDet] Valid boxes after coordinate transformation: " << results_.size() << std::endl;

    // 执行NMS去除重复检测
    if(results_.size() > 0) {
        std::cout << "[SlamDet] Applying NMS..." << std::endl;
        NMS(results_, nms_threshold_);
        std::cout << "[SlamDet] After NMS: " << results_.size() << " boxes" << std::endl;
    }

    delete[] output_head;

    std::cout << "[SlamDet] Final detection results: " << results_.size() << std::endl;

    return 0;
}
